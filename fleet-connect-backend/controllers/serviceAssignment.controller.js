import db from "../models/index.js";
import pushNotificationService from "../services/pushNotification.service.js";

const { ServiceAssignment, ServiceRequest, Mechanic, Driver, TruckRepairCompany, PartnerCompany } = db;

const serviceAssignmentController = {
  // POST /api/assignments/create
  createAssignment: async (req, res) => {
    try {
      const { service_request_id, mechanic_id, estimated_arrival, notes } = req.body;
      const { id: assigned_by_id, role } = req.user;

      // Verify user has permission to create assignments
      if (role !== "admin" && role !== "truck_repair_company") {
        return res.status(403).json({ message: "Unauthorized to create assignments" });
      }

      // Verify service request exists
      const serviceRequest = await ServiceRequest.findByPk(service_request_id, {
        include: [{ model: Driver, as: "Driver" }]
      });
      
      if (!serviceRequest) {
        return res.status(404).json({ message: "Service request not found" });
      }

      // Check if already assigned
      const existingAssignment = await ServiceAssignment.findOne({
        where: { service_request_id }
      });

      if (existingAssignment) {
        return res.status(400).json({ message: "Service request already assigned" });
      }

      // Verify mechanic exists and is available
      const mechanic = await Mechanic.findByPk(mechanic_id, {
        include: [{ model: TruckRepairCompany, as: "company" }]
      });

      if (!mechanic || !mechanic.is_active) {
        return res.status(404).json({ message: "Mechanic not found or inactive" });
      }

      // Create assignment
      const assignment = await ServiceAssignment.create({
        service_request_id,
        mechanic_id,
        assigned_by: assigned_by_id,
        estimated_arrival,
        notes,
        status: "assigned",
      });

      // Update service request status
      await ServiceRequest.update(
        { status: "assigned", assigned_at: new Date() },
        { where: { id: service_request_id } }
      );

      // Send push notification to mechanic
      try {
        await pushNotificationService.sendToUser(
          mechanic_id,
          "mechanic",
          {
            title: "New Service Assignment",
            body: `You have been assigned to help ${serviceRequest.Driver?.name || 'a driver'}`,
            data: {
              type: "new_assignment",
              service_request_id,
              assignment_id: assignment.id,
            },
          }
        );
      } catch (notificationError) {
        console.error("Error sending assignment notification:", notificationError);
      }

      // Emit real-time notification
      const io = req.app.get('io');
      if (io) {
        io.to(`service_request_${service_request_id}`).emit('assignment_created', {
          assignment_id: assignment.id,
          mechanic_id,
          mechanic_name: mechanic.name,
          estimated_arrival,
          status: "assigned",
        });
      }

      res.status(201).json({
        message: "Assignment created successfully",
        assignment,
      });
    } catch (error) {
      console.error("Error creating assignment:", error);
      res.status(500).json({ message: "Failed to create assignment" });
    }
  },

  // PUT /api/assignments/:id/accept
  acceptAssignment: async (req, res) => {
    try {
      const { id: assignment_id } = req.params;
      const { estimated_arrival, notes } = req.body;
      const { id: mechanic_id, role } = req.user;

      if (role !== "mechanic") {
        return res.status(403).json({ message: "Only mechanics can accept assignments" });
      }

      const assignment = await ServiceAssignment.findByPk(assignment_id, {
        include: [
          { 
            model: ServiceRequest, 
            include: [{ model: Driver, as: "Driver" }] 
          },
          { model: Mechanic }
        ]
      });

      if (!assignment) {
        return res.status(404).json({ message: "Assignment not found" });
      }

      if (assignment.mechanic_id !== mechanic_id) {
        return res.status(403).json({ message: "Unauthorized to accept this assignment" });
      }

      if (assignment.status !== "assigned") {
        return res.status(400).json({ message: "Assignment cannot be accepted in current status" });
      }

      // Update assignment
      await assignment.update({
        status: "accepted",
        accepted_at: new Date(),
        estimated_arrival: estimated_arrival || assignment.estimated_arrival,
        notes: notes || assignment.notes,
      });

      // Update service request status
      await ServiceRequest.update(
        { status: "en_route" },
        { where: { id: assignment.service_request_id } }
      );

      // Send push notification to driver
      try {
        await pushNotificationService.sendToUser(
          assignment.ServiceRequest.driver_id,
          "driver",
          pushNotificationService.notifications.mechanicAccepted(assignment.Mechanic.name)
        );
      } catch (notificationError) {
        console.error("Error sending acceptance notification:", notificationError);
      }

      // Emit real-time notification
      const io = req.app.get('io');
      if (io) {
        io.to(`service_request_${assignment.service_request_id}`).emit('assignment_accepted', {
          assignment_id,
          mechanic_name: assignment.Mechanic.name,
          estimated_arrival,
          status: "accepted",
        });
      }

      res.json({
        message: "Assignment accepted successfully",
        assignment,
      });
    } catch (error) {
      console.error("Error accepting assignment:", error);
      res.status(500).json({ message: "Failed to accept assignment" });
    }
  },

  // PUT /api/assignments/:id/reject
  rejectAssignment: async (req, res) => {
    try {
      const { id: assignment_id } = req.params;
      const { rejection_reason } = req.body;
      const { id: mechanic_id, role } = req.user;

      if (role !== "mechanic") {
        return res.status(403).json({ message: "Only mechanics can reject assignments" });
      }

      const assignment = await ServiceAssignment.findByPk(assignment_id, {
        include: [
          { model: ServiceRequest },
          { model: Mechanic }
        ]
      });

      if (!assignment) {
        return res.status(404).json({ message: "Assignment not found" });
      }

      if (assignment.mechanic_id !== mechanic_id) {
        return res.status(403).json({ message: "Unauthorized to reject this assignment" });
      }

      if (assignment.status !== "assigned") {
        return res.status(400).json({ message: "Assignment cannot be rejected in current status" });
      }

      // Update assignment
      await assignment.update({
        status: "rejected",
        rejected_at: new Date(),
        rejection_reason,
      });

      // Reset service request status to pending for reassignment
      await ServiceRequest.update(
        { status: "pending" },
        { where: { id: assignment.service_request_id } }
      );

      // Emit real-time notification
      const io = req.app.get('io');
      if (io) {
        io.to(`service_request_${assignment.service_request_id}`).emit('assignment_rejected', {
          assignment_id,
          mechanic_name: assignment.Mechanic.name,
          rejection_reason,
          status: "rejected",
        });
      }

      res.json({
        message: "Assignment rejected successfully",
        assignment,
      });
    } catch (error) {
      console.error("Error rejecting assignment:", error);
      res.status(500).json({ message: "Failed to reject assignment" });
    }
  },

  // PUT /api/assignments/:id/start-work
  startWork: async (req, res) => {
    try {
      const { id: assignment_id } = req.params;
      const { notes } = req.body;
      const { id: mechanic_id, role } = req.user;

      if (role !== "mechanic") {
        return res.status(403).json({ message: "Only mechanics can start work" });
      }

      const assignment = await ServiceAssignment.findByPk(assignment_id, {
        include: [{ model: ServiceRequest }]
      });

      if (!assignment || assignment.mechanic_id !== mechanic_id) {
        return res.status(404).json({ message: "Assignment not found" });
      }

      if (assignment.status !== "accepted") {
        return res.status(400).json({ message: "Work can only be started on accepted assignments" });
      }

      // Update assignment and service request
      await assignment.update({
        work_started_at: new Date(),
        actual_arrival: new Date(),
        notes: notes || assignment.notes,
      });

      await ServiceRequest.update(
        { status: "in_progress" },
        { where: { id: assignment.service_request_id } }
      );

      // Emit real-time notification
      const io = req.app.get('io');
      if (io) {
        io.to(`service_request_${assignment.service_request_id}`).emit('work_started', {
          assignment_id,
          started_at: new Date(),
        });
      }

      res.json({
        message: "Work started successfully",
        assignment,
      });
    } catch (error) {
      console.error("Error starting work:", error);
      res.status(500).json({ message: "Failed to start work" });
    }
  },

  // PUT /api/assignments/:id/complete
  completeWork: async (req, res) => {
    try {
      const { id: assignment_id } = req.params;
      const { notes, completion_notes } = req.body;
      const { id: mechanic_id, role } = req.user;

      if (role !== "mechanic") {
        return res.status(403).json({ message: "Only mechanics can complete work" });
      }

      const assignment = await ServiceAssignment.findByPk(assignment_id, {
        include: [
          {
            model: ServiceRequest,
            include: [{ model: Driver, as: "Driver" }]
          },
          { model: Mechanic }
        ]
      });

      if (!assignment || assignment.mechanic_id !== mechanic_id) {
        return res.status(404).json({ message: "Assignment not found" });
      }

      if (assignment.status !== "in_progress") {
        return res.status(400).json({ message: "Work can only be completed on in-progress assignments" });
      }

      // Update assignment with completion details
      await assignment.update({
        status: "completed",
        completed_at: new Date(),
        completion_notes: completion_notes || notes || assignment.notes,
      });

      // Update service request status to completed
      await ServiceRequest.update(
        {
          status: "completed",
          completed_at: new Date()
        },
        { where: { id: assignment.service_request_id } }
      );

      // Send push notification to driver
      try {
        await pushNotificationService.sendToUser(
          assignment.ServiceRequest.driver_id,
          "driver",
          pushNotificationService.notifications.serviceCompleted(assignment.Mechanic.name)
        );
      } catch (notificationError) {
        console.error("Error sending completion notification:", notificationError);
      }

      // Emit real-time notification
      const io = req.app.get('io');
      if (io) {
        io.to(`service_request_${assignment.service_request_id}`).emit('service_completed', {
          assignment_id,
          mechanic_name: assignment.Mechanic.name,
          completed_at: new Date(),
          status: "completed",
        });
      }

      res.json({
        message: "Work completed successfully",
        assignment,
      });
    } catch (error) {
      console.error("Error completing work:", error);
      res.status(500).json({ message: "Failed to complete work" });
    }
  },

  // GET /api/assignments/mechanic/:id
  getMechanicAssignments: async (req, res) => {
    try {
      const { id: mechanic_id } = req.params;
      const { id: user_id, role } = req.user;
      const { status, limit = 20, offset = 0 } = req.query;

      // Check authorization
      if (role === "mechanic" && user_id !== mechanic_id) {
        return res.status(403).json({ message: "Unauthorized to view these assignments" });
      }

      const whereClause = { mechanic_id };
      if (status) {
        whereClause.status = status;
      }

      const assignments = await ServiceAssignment.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: ServiceRequest,
            include: [{ model: Driver, as: "Driver" }]
          }
        ],
        order: [["assigned_at", "DESC"]],
        limit: parseInt(limit),
        offset: parseInt(offset),
      });

      res.json({
        assignments: assignments.rows,
        total: assignments.count,
        limit: parseInt(limit),
        offset: parseInt(offset),
      });
    } catch (error) {
      console.error("Error fetching mechanic assignments:", error);
      res.status(500).json({ message: "Failed to fetch assignments" });
    }
  },

  // POST /api/assignments/accept - Mechanic accepts a service request
  acceptServiceRequest: async (req, res) => {
    try {
      const { service_request_id, estimated_arrival } = req.body;
      const mechanic_id = req.user.id;

      console.log(`🔧 Mechanic ${mechanic_id} attempting to accept service request ${service_request_id}`);

      // Check if service request exists and is still pending
      const serviceRequest = await ServiceRequest.findByPk(service_request_id, {
        include: [{ model: Driver, as: "Driver" }]
      });

      if (!serviceRequest) {
        return res.status(404).json({ message: "Service request not found" });
      }

      if (serviceRequest.status !== 'pending') {
        return res.status(400).json({
          message: "Service request is no longer available",
          current_status: serviceRequest.status
        });
      }

      // Optimized race condition protection - quick pre-check before transaction
      console.log('🔍 Pre-transaction validation...');

      // Quick check without transaction first to fail fast
      const quickCheck = await ServiceRequest.findByPk(service_request_id);
      if (!quickCheck || quickCheck.status !== 'pending') {
        console.log('❌ Quick check failed - service request not available');
        return res.status(400).json({
          message: "Service request is no longer available",
          current_status: quickCheck?.status || 'not_found'
        });
      }

      const existingQuickCheck = await ServiceAssignment.findOne({
        where: { service_request_id }
      });
      if (existingQuickCheck) {
        console.log('❌ Quick check failed - already assigned');
        return res.status(409).json({
          message: "Service request already accepted by another mechanic"
        });
      }

      console.log('✅ Pre-transaction validation passed');

      // Get mechanic information
      console.log('👤 Validating mechanic...');
      const mechanic = await Mechanic.findByPk(mechanic_id);

      if (!mechanic || !mechanic.is_active) {
        console.log('❌ Mechanic validation failed');
        return res.status(403).json({ message: "Mechanic not found or inactive" });
      }

      console.log('✅ Mechanic validation passed');

      // Optimized single transaction for assignment creation
      let assignment;
      try {
        console.log('🔄 Starting optimized transaction...');

        assignment = await db.sequelize.transaction(async (t) => {
          // Final check and update in single atomic operation
          const [updatedRows] = await ServiceRequest.update(
            {
              status: 'assigned',
              assigned_at: new Date()
            },
            {
              where: {
                id: service_request_id,
                status: 'pending' // Only update if still pending
              },
              transaction: t
            }
          );

          // If no rows were updated, it means the request was already assigned
          if (updatedRows === 0) {
            throw new Error('ALREADY_ASSIGNED');
          }

          // Create assignment
          return await ServiceAssignment.create({
            service_request_id,
            mechanic_id,
            assigned_by: null, // Self-assigned (no company assignment)
            estimated_arrival: estimated_arrival || new Date(Date.now() + 30 * 60 * 1000), // Default 30 min
            status: 'accepted',
            accepted_at: new Date(),
            notes: 'Self-accepted via mobile app'
          }, { transaction: t });
        });

        console.log('✅ Transaction completed successfully');
      } catch (transactionError) {
        console.error(`❌ Transaction error for service request ${service_request_id}:`, transactionError.message);

        if (transactionError.message === 'SERVICE_NOT_AVAILABLE') {
          return res.status(400).json({
            message: "Service request is no longer available",
            error_code: "SERVICE_NOT_AVAILABLE"
          });
        }

        if (transactionError.message === 'ALREADY_ASSIGNED') {
          return res.status(409).json({
            message: "Service request already accepted by another mechanic",
            error_code: "ALREADY_ASSIGNED"
          });
        }

        // Handle database constraint errors
        if (transactionError.name === 'SequelizeUniqueConstraintError') {
          return res.status(409).json({
            message: "Service request already assigned",
            error_code: "CONSTRAINT_VIOLATION"
          });
        }

        // Handle deadlock errors
        if (transactionError.name === 'SequelizeTimeoutError' ||
            transactionError.message.includes('deadlock')) {
          return res.status(503).json({
            message: "Service temporarily unavailable, please try again",
            error_code: "DEADLOCK_RETRY"
          });
        }

        throw transactionError; // Re-throw unexpected errors
      }

      console.log(`✅ Service request ${service_request_id} accepted by mechanic ${mechanic_id}`);

      // Broadcast real-time updates via Socket.IO
      const io = req.app.get('io');
      if (io) {
        const updateData = {
          type: 'service_request_accepted',
          service_request_id,
          assignment_id: assignment.id,
          mechanic_id,
          mechanic_name: mechanic.name,
          mechanic_company: null, // TODO: Add company info later
          estimated_arrival: assignment.estimated_arrival,
          timestamp: new Date().toISOString(),
        };

        // Notify all mechanics that this request is no longer available
        io.emit('service_request_unavailable', {
          service_request_id,
          reason: 'accepted_by_another_mechanic',
          accepted_by: mechanic.name,
          timestamp: new Date().toISOString(),
        });

        // Notify the driver that a mechanic has accepted
        io.to(`driver_${serviceRequest.driver_id}`).emit('mechanic_assigned', {
          ...updateData,
          service_request: serviceRequest,
          mechanic: {
            id: mechanic.id,
            name: mechanic.name,
            company: null, // TODO: Add company info later
            phone: mechanic.phone,
            location: {
              latitude: mechanic.last_location_lat,
              longitude: mechanic.last_location_lng,
              lastUpdated: mechanic.last_location_updated,
            }
          }
        });

        // Notify the accepting mechanic with full details
        io.to(`mechanic_${mechanic_id}`).emit('assignment_confirmed', {
          ...updateData,
          service_request: serviceRequest,
          assignment: assignment,
        });

        console.log('📡 Real-time updates broadcasted for service request acceptance');
      }

      res.status(201).json({
        message: 'Service request accepted successfully',
        assignment: assignment,
        service_request: serviceRequest,
        mechanic: {
          id: mechanic.id,
          name: mechanic.name,
          company: null, // TODO: Add company info later
        }
      });

    } catch (error) {
      console.error('❌ Error accepting service request:', error);

      if (error.message === 'Service request already assigned') {
        return res.status(409).json({
          message: "Service request already accepted by another mechanic"
        });
      }

      res.status(500).json({ message: "Failed to accept service request" });
    }
  },
};

export default serviceAssignmentController;
