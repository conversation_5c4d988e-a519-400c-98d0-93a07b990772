import io from "socket.io-client";
import { store } from "../store/store";
import {
  updateServiceInHistory,
  setActiveRequest,
  handleMechanicAccepted,
  setWaitingForMechanic,
  updateMechanicLocation,
  clearActiveRequest,
} from "../store/slices/serviceSlice";

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.listeners = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.connectionCheckInterval = null;
  }

  async connect() {
    try {
      if (this.socket && this.isConnected) {
        console.log("🔌 Socket already connected");
        return;
      }

      // Safely get state with error handling
      let state, token, user;
      try {
        state = store.getState();
        token = state?.auth?.token;
        user = state?.auth?.user;
      } catch (storeError) {
        console.error("❌ Error accessing store:", storeError);
        this.scheduleReconnect();
        return;
      }

      if (!token || !user) {
        console.error("❌ No auth token or user found");
        return;
      }

      console.log("🔌 Driver App: Connecting to Socket.IO server...");
      console.log("🔑 Using token:", token ? "Token exists" : "No token");
      console.log(
        "🔑 Token preview:",
        token ? token.substring(0, 50) + "..." : "No token"
      );
      console.log("🔑 Token length:", token ? token.length : 0);
      console.log("👤 User ID:", user?.id);
      console.log("👤 User role:", user?.role || "No role");

      // Connect to Socket.IO server - Production URL
      const socketUrl = "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com";
      this.socket = io(socketUrl, {
        auth: {
          token: token,
        },
        transports: ["websocket", "polling"],
        timeout: 10000,
        forceNew: true,
      });

      this.setupEventListeners();

      // Join driver room for receiving mechanic assignments
      this.socket.on("connect", () => {
        console.log("✅ Driver Socket.IO connected");
        this.isConnected = true;
        this.reconnectAttempts = 0;

        // Join driver-specific room
        this.joinDriverRoom(user.id);

        // Notify listeners
        this.notifyListeners("connected", { connected: true });
      });
    } catch (error) {
      console.error("❌ Socket connection error:", error);
      this.scheduleReconnect();
    }
  }

  setupEventListeners() {
    if (!this.socket) return;

    this.socket.on("disconnect", (reason) => {
      console.log("❌ Driver Socket disconnected:", reason);
      this.isConnected = false;
      this.notifyListeners("disconnected", { connected: false });

      if (reason === "io server disconnect") {
        // Server disconnected, need to reconnect manually
        this.scheduleReconnect();
      }
    });

    this.socket.on("connect_error", (error) => {
      console.error("❌ Driver Socket connection error:", error);
      this.isConnected = false;

      // If authentication error, the token might be invalid/expired
      if (error.message === "Authentication error") {
        console.warn(
          "⚠️ Authentication failed - token may be invalid or expired"
        );
        console.warn(
          "💡 This usually means the JWT secret changed or token was signed with different secret"
        );
        console.warn("🔄 Please logout and login again to get a fresh token");

        // Clear the invalid token and force logout
        this.clearAuthAndForceLogout();

        // Don't keep retrying with invalid token
        this.reconnectAttempts = this.maxReconnectAttempts;
        return; // Don't schedule reconnect for auth errors
      }

      this.scheduleReconnect();
    });

    // Handle mechanic assignment
    this.socket.on("mechanic_assigned", (data) => {
      console.log("🔧 Mechanic assigned to service request:", data);
      this.handleMechanicAssigned(data);
    });

    // Handle service status updates
    this.socket.on("service_status_update", (data) => {
      console.log("🔄 Service status update:", data);
      this.handleServiceStatusUpdate(data);
    });

    // Handle mechanic status updates
    this.socket.on("mechanic_status_update", (data) => {
      console.log("🔧 Mechanic status update:", data);
      this.handleMechanicStatusUpdate(data);
    });

    // Handle service completion
    this.socket.on("service_completed", (data) => {
      console.log("✅ Service completed:", data);
      this.handleServiceCompleted(data);
    });

    // Handle new messages
    this.socket.on("new_message", (data) => {
      console.log("💬 New message received:", data);
      this.handleNewMessage(data);
    });

    // Handle location updates
    this.socket.on("location_update", (data) => {
      console.log("📍 Location update received:", data);
      this.handleLocationUpdate(data);
    });
  }

  joinDriverRoom(driverId) {
    if (!this.socket || !this.isConnected) return;

    // Join driver-specific room for receiving notifications
    this.socket.emit("join_driver_room", { driverId });
    console.log(`🚗 Driver joined room: driver_${driverId}`);
  }

  joinServiceRoom(serviceRequestId) {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit("join_service_request", serviceRequestId);
    console.log("🏠 Joined service room:", serviceRequestId);
  }

  leaveServiceRoom(serviceRequestId) {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit("leave_service_request", serviceRequestId);
    console.log("🚪 Left service room:", serviceRequestId);
  }

  handleMechanicAssigned(data) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    console.log(`🔧 [${timestamp}] SocketService: handleMechanicAssigned called:`, data);

    const { service_request, mechanic, estimated_arrival, service_request_id } =
      data;
    const serviceId = service_request_id || service_request?.id;

    if (!serviceId) {
      console.error("No service ID found in mechanic_assigned payload");
      return;
    }

    // Check current state before making changes
    const currentState = store.getState();
    console.log(`🔧 [${timestamp}] Current state before mechanic assignment:`, {
      waitingForMechanic: currentState.service.waitingForMechanic,
      activeRequestId: currentState.service.activeRequest?.id,
      matchesCurrentRequest: currentState.service.activeRequest?.id === serviceId,
    });

    // Update service history
    store.dispatch(
      updateServiceInHistory({
        serviceId,
        updates: {
          status: "accepted",
          mechanicName: mechanic.name,
          mechanicPhone: mechanic.phone,
          mechanicId: mechanic.id,
          estimatedArrival: estimated_arrival,
        },
      })
    );

    // Update active request if it matches
    try {
      const state = store.getState();
      if (state?.service?.activeRequest?.id === serviceId) {
        console.log("🔄 Updating active request with mechanic assignment");

        // Extract mechanic location
        const mechanicLocation = mechanic.location;
        console.log("📍 SocketService: Mechanic location data:", mechanicLocation);

        // Use the proper Redux action for mechanic acceptance
        store.dispatch(
          handleMechanicAccepted({
            mechanic: {
              id: mechanic.id,
              name: mechanic.name,
              phone: mechanic.phone,
              location: mechanicLocation,
            },
            estimatedArrival: estimated_arrival,
          })
        );

        // Update mechanic location if provided
        if (mechanicLocation && mechanicLocation.latitude && mechanicLocation.longitude) {
          console.log("📍 SocketService: Updating mechanic location:", {
            latitude: mechanicLocation.latitude,
            longitude: mechanicLocation.longitude,
          });

          store.dispatch(
            updateMechanicLocation({
              latitude: parseFloat(mechanicLocation.latitude),
              longitude: parseFloat(mechanicLocation.longitude),
              lastUpdated: mechanicLocation.lastUpdated,
            })
          );
        } else {
          console.warn("⚠️ SocketService: No valid mechanic location provided");
        }

        // Stop waiting state
        console.log('🔌 SocketService: Mechanic assigned - setting waitingForMechanic to false');
        store.dispatch(setWaitingForMechanic(false));

        // Log final state for validation
        setTimeout(() => {
          const finalState = store.getState();
          console.log("🔍 DRIVER: Final state after mechanic assignment:", {
            waitingForMechanic: finalState.service.waitingForMechanic,
            activeRequestStatus: finalState.service.activeRequest?.status,
            mechanicId: finalState.service.activeRequest?.mechanicId,
            mechanicName: finalState.service.activeRequest?.mechanicName,
            assignedAt: finalState.service.activeRequest?.assignedAt,
            mechanicLocation: finalState.service.mechanicLocation,
          });
        }, 100);
      }
    } catch (storeError) {
      console.error("❌ Error updating active request:", storeError);
    }

    // Notify listeners for UI updates
    this.notifyListeners("mechanic_assigned", data);
  }

  handleServiceCompleted(data) {
    const { serviceId, completedBy } = data;

    store.dispatch(
      updateServiceInHistory({
        serviceId,
        updates: {
          status: "completed",
          completedBy,
          completedAt: new Date().toISOString(),
        },
      })
    );

    // Clear active request if it matches
    try {
      const state = store.getState();
      if (state?.service?.activeRequest?.id === serviceId) {
        store.dispatch(setActiveRequest(null));
      }
    } catch (storeError) {
      console.error("❌ Error clearing active request:", storeError);
    }

    this.notifyListeners("service_completed", data);
  }

  handleServiceStatusUpdate(data) {
    const { serviceId, status, message, timestamp } = data;
    console.log(`🔄 Service ${serviceId} status updated to: ${status}`);

    // Update service in history
    store.dispatch(
      updateServiceInHistory({
        serviceId,
        updates: {
          status,
          statusMessage: message,
          lastUpdated: timestamp || new Date().toISOString(),
        },
      })
    );

    // Update active request if it matches
    try {
      const state = store.getState();
      if (state?.service?.activeRequest?.id === serviceId) {
        console.log("🔄 Updating active request status");
        store.dispatch(
          setActiveRequest({
            ...state.service.activeRequest,
            status,
            statusMessage: message,
            lastUpdated: timestamp || new Date().toISOString(),
          })
        );
      }
    } catch (storeError) {
      console.error("❌ Error updating active request status:", storeError);
    }

    // Notify listeners
    this.notifyListeners("service_status_update", data);
  }

  handleMechanicStatusUpdate(data) {
    const { mechanic_id, status, message, timestamp } = data;
    console.log(`🔧 Mechanic ${mechanic_id} status: ${status}`);

    // Update active request if mechanic matches
    try {
      const state = store.getState();
      if (state?.service?.activeRequest?.mechanicId === mechanic_id) {
        console.log("🔄 Updating mechanic status in active request");
        store.dispatch(
          setActiveRequest({
            ...state.service.activeRequest,
            mechanicStatus: status,
            mechanicStatusMessage: message,
            mechanicLastUpdate: timestamp || new Date().toISOString(),
          })
        );
      }
    } catch (storeError) {
      console.error("❌ Error updating mechanic status:", storeError);
    }

    // Notify listeners
    this.notifyListeners("mechanic_status_update", data);
  }

  handleNewMessage(data) {
    const {
      serviceId,
      serviceRequestId,
      message,
      senderType,
      senderId,
      senderName,
      timestamp,
      messageType,
    } = data;

    // Only handle messages from mechanics (not our own messages)
    if (senderType === "mechanic") {
      console.log(
        `💬 New message from mechanic in service ${
          serviceId || serviceRequestId
        }`
      );

      // Notify chat components
      this.notifyListeners("new_message", {
        serviceId: serviceId || serviceRequestId,
        message,
        sender: senderId,
        senderType,
        senderName,
        timestamp,
        messageType: messageType || "text",
      });
    }
  }

  handleLocationUpdate(data) {
    const { userId, userType, latitude, longitude, serviceRequestId } = data;
    console.log("📍 Location update received:", data);

    // Only handle mechanic location updates
    if (userType === "mechanic") {
      console.log(`📍 Mechanic ${userId} location update for service ${serviceRequestId}`);

      // Update mechanic location in Redux store
      try {
        const state = store.getState();
        if (state?.service?.activeRequest?.id === serviceRequestId) {
          console.log("🔄 Updating mechanic location in active request");
          store.dispatch(
            updateMechanicLocation({
              latitude,
              longitude,
              timestamp: new Date().toISOString(),
            })
          );
        }
      } catch (storeError) {
        console.error("❌ Error updating mechanic location:", storeError);
      }
    }

    this.notifyListeners("location_update", data);
  }

  // Send message to mechanic
  sendMessage(serviceRequestId, message, messageType = "text") {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit("send_message", {
      serviceRequestId,
      message,
      messageType,
      timestamp: new Date().toISOString(),
    });
  }

  // Share location with mechanic
  shareLocation(
    serviceRequestId,
    latitude,
    longitude,
    heading = null,
    speed = null
  ) {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit("share_location", {
      serviceRequestId,
      latitude,
      longitude,
      heading,
      speed,
    });
  }

  // Send location update (for location tracking)
  sendLocationUpdate(location) {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit("location_update", {
      latitude: location.latitude,
      longitude: location.longitude,
      accuracy: location.accuracy,
      speed: location.speed,
      heading: location.heading,
      timestamp: new Date().toISOString(),
    });
  }

  // Send driver status update
  sendStatusUpdate(isActive) {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit("status_update", {
      is_active: isActive,
      timestamp: new Date().toISOString(),
    });
  }

  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error("❌ Max reconnection attempts reached");
      return;
    }

    // Check if store is available before attempting reconnection
    try {
      if (!store || !store.getState) {
        console.log("🔄 Store not ready, delaying reconnection...");
        setTimeout(() => this.scheduleReconnect(), 2000);
        return;
      }
    } catch (error) {
      console.log("🔄 Store not ready, delaying reconnection...");
      setTimeout(() => this.scheduleReconnect(), 2000);
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

    console.log(
      `🔄 Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`
    );

    setTimeout(() => {
      this.connect();
    }, delay);
  }

  clearAuthAndForceLogout() {
    try {
      // Import logout action dynamically to avoid circular dependencies
      const { logoutDriver } = require("../store/slices/authSlice");

      console.warn("🚪 Clearing invalid authentication and forcing logout...");
      console.warn(
        "💡 This is likely due to JWT secret mismatch - please login again"
      );

      // Dispatch logout action to clear Redux state and AsyncStorage
      store.dispatch(logoutDriver());

      // Navigate to login screen (this will be handled by the auth state change)
      console.warn("🔄 User will be redirected to login screen");
    } catch (error) {
      console.error("❌ Error during forced logout:", error);
    }
  }

  // Event listener management
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
      if (callbacks.length === 0) {
        this.listeners.delete(event);
      }
    }
  }

  notifyListeners(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }

  getConnectionStatus() {
    return {
      connected: this.isConnected,
      socket: !!this.socket,
    };
  }

  disconnect() {
    if (this.socket) {
      console.log("🔌 Disconnecting from socket");
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }

    // Clear connection check interval
    if (this.connectionCheckInterval) {
      clearInterval(this.connectionCheckInterval);
      this.connectionCheckInterval = null;
    }
  }
}

export default new SocketService();
